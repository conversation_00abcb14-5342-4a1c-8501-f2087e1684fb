import { expect, test } from '@playwright/test';

test.describe('Budget Versioning End-to-End Workflows', () => {
	test.beforeEach(async ({ page }) => {
		// Sign in with test user
		await page.goto('/auth/signin');
		await page.fill('input[name="email"]', '<EMAIL>');
		await page.fill('input[name="password"]', 'testtest');
		await page.click('button:has-text("Sign In")');
		await page.waitForURL(/\/org\/.+/);
	});

	test('complete budget import workflow with versioning', async ({ page }) => {
		// Navigate to admin tools and generate demo project
		await page.goto('/admin-tools');
		await expect(page.locator('h1:has-text("Admin Tools")')).toBeVisible();

		// Generate demo project
		const budgetInput = page.locator('input[name="totalBudget"]');
		if (await budgetInput.count()) {
			await budgetInput.fill('1000000');
		}
		await page.locator('#riba-confirm-checkbox').click();
		await page.click('button:has-text("Generate RIBA Demo Project Data")');

		// Wait for project creation and navigate to it
		const goToProject = page.getByRole('button', { name: 'Go to Project' });
		await goToProject.waitFor({ state: 'visible', timeout: 120000 });
		await goToProject.click();

		// Navigate to budget import page
		await expect(page).toHaveURL(/\/org\/.+\/projects\/.+\/overview/);
		const importUrl = page.url().replace('/overview', '/budget/import');
		await page.goto(importUrl);

		// Test file upload (mock a CSV file)
		const fileContent = `Code,Description,Quantity,Unit,Unit Rate
1.1.1,Test Item 1,10,each,100
1.1.2,Test Item 2,5,m2,200`;

		// Create a temporary file for upload
		const fileInput = page.locator('input[type="file"]');
		await fileInput.setInputFiles({
			name: 'test-budget.csv',
			mimeType: 'text/csv',
			buffer: Buffer.from(fileContent),
		});

		// Wait for file processing
		await expect(page.getByText('File uploaded successfully')).toBeVisible({ timeout: 10000 });

		// Proceed through import wizard steps
		await page.click('button:has-text("Next")');

		// Step 2: Column mapping - verify auto-mapping worked
		await expect(page.getByText('Step 2: Map Columns')).toBeVisible();
		await page.click('button:has-text("Next")');

		// Step 3: Row classification
		await expect(page.getByText('Step 3: Classify Rows')).toBeVisible();
		await page.click('button:has-text("Next")');

		// Step 4: WBS mapping
		await expect(page.getByText('Step 4: Map WBS Codes')).toBeVisible();
		await page.click('button:has-text("Next")');

		// Step 5: Review import with preview
		await expect(page.getByText('Step 5: Review Import')).toBeVisible();

		// Verify preview panel is shown
		await expect(page.getByText('Preview vs Active Budget')).toBeVisible();

		// Check for preview summary chips
		await expect(page.getByText(/Items Added/)).toBeVisible();
		await expect(page.getByText(/Net Cost Delta/)).toBeVisible();

		// Verify diff tabs are present
		await expect(page.getByRole('tab', { name: /Added/ })).toBeVisible();
		await expect(page.getByRole('tab', { name: /Removed/ })).toBeVisible();
		await expect(page.getByRole('tab', { name: /Changed/ })).toBeVisible();

		// Complete the import
		await page.click('button:has-text("Import")');

		// Verify import success
		await expect(page.getByText(/Import completed successfully/)).toBeVisible({ timeout: 30000 });

		// Navigate to budget page to verify version was created
		const budgetUrl = page.url().replace('/budget/import', '/budget');
		await page.goto(budgetUrl);

		// Open version history
		await page.getByRole('button', { name: 'Version History' }).click();
		await expect(page.getByText('Budget Version History')).toBeVisible();

		// Verify import version is listed
		await expect(page.getByText('Import Version')).toBeVisible();
		await expect(page.getByText('test-budget.csv')).toBeVisible();

		// Test version comparison
		const compareButton = page.getByRole('button', { name: 'Compare to Active' }).first();
		if (await compareButton.isVisible()) {
			await compareButton.click();
			// Verify diff is displayed (implementation depends on UI)
			await page.waitForTimeout(2000); // Allow time for diff to load
		}
	});

	test('stage completion creates budget version', async ({ page }) => {
		// Navigate to admin tools and generate demo project
		await page.goto('/admin-tools');
		await page.locator('#riba-confirm-checkbox').click();
		await page.click('button:has-text("Generate RIBA Demo Project Data")');

		const goToProject = page.getByRole('button', { name: 'Go to Project' });
		await goToProject.waitFor({ state: 'visible', timeout: 120000 });
		await goToProject.click();

		// Navigate to first stage gateway
		await expect(page).toHaveURL(/\/org\/.+\/projects\/.+\/overview/);
		const gatewayUrl = page.url().replace('/overview', '/stage-1/gateway');
		await page.goto(gatewayUrl);

		// Complete checklist items if any exist
		const checklistItems = page.locator('[data-testid="checklist-item"]');
		const itemCount = await checklistItems.count();

		for (let i = 0; i < itemCount; i++) {
			const item = checklistItems.nth(i);
			const statusSelect = item.locator('select, [role="combobox"]').first();
			if (await statusSelect.isVisible()) {
				await statusSelect.click();
				await page.getByRole('option', { name: 'Complete' }).click();
			}
		}

		// Check stage readiness
		await page.click('button:has-text("Check Completion Readiness")');
		await expect(page.getByText(/Stage is ready for completion/)).toBeVisible({ timeout: 10000 });

		// Complete the stage
		const notesTextarea = page.locator('textarea[placeholder*="notes"]');
		if (await notesTextarea.isVisible()) {
			await notesTextarea.fill('E2E test stage completion');
		}

		await page.click('button:has-text("Complete Stage")');

		// Verify redirect to project overview
		await expect(page).toHaveURL(/\/org\/.+\/projects\/.+\/overview/, { timeout: 30000 });
		await expect(page.getByText(/Stage completed successfully/)).toBeVisible();

		// Navigate to budget page and check version history
		const budgetUrl = page.url().replace('/overview', '/budget');
		await page.goto(budgetUrl);

		await page.getByRole('button', { name: 'Version History' }).click();
		await expect(page.getByText('Budget Version History')).toBeVisible();

		// Verify stage version was created
		await expect(page.getByText(/Stage Completion/)).toBeVisible();
		await expect(page.getByText('stage')).toBeVisible();
	});

	test('version activation workflow', async ({ page }) => {
		// Setup: Create project with multiple versions
		await page.goto('/admin-tools');
		await page.locator('#riba-confirm-checkbox').click();
		await page.click('button:has-text("Generate RIBA Demo Project Data")');

		const goToProject = page.getByRole('button', { name: 'Go to Project' });
		await goToProject.waitFor({ state: 'visible', timeout: 120000 });
		await goToProject.click();

		// Navigate to budget page
		const budgetUrl = page.url().replace('/overview', '/budget');
		await page.goto(budgetUrl);

		// Open version history
		await page.getByRole('button', { name: 'Version History' }).click();
		await expect(page.getByText('Budget Version History')).toBeVisible();

		// Find a non-active version to activate
		const versionRows = page.locator('table tbody tr');
		const rowCount = await versionRows.count();

		for (let i = 0; i < rowCount; i++) {
			const row = versionRows.nth(i);
			const isActive = await row.getByText('Active').isVisible();

			if (!isActive) {
				// Click the actions menu for this version
				const actionsButton = row.locator('button[aria-haspopup="menu"]');
				await actionsButton.click();

				// Look for activate option
				const activateOption = page.getByRole('menuitem', { name: /Activate/ });
				if (await activateOption.isVisible()) {
					await activateOption.click();

					// Verify activation success
					await expect(page.getByText(/Version activated/)).toBeVisible({ timeout: 10000 });

					// Verify page reload (budget should reflect new active version)
					await page.waitForLoadState('networkidle');
					break;
				}
			}
		}
	});

	test('import duplicate detection', async ({ page }) => {
		// Navigate to admin tools and generate demo project
		await page.goto('/admin-tools');
		await page.locator('#riba-confirm-checkbox').click();
		await page.click('button:has-text("Generate RIBA Demo Project Data")');

		const goToProject = page.getByRole('button', { name: 'Go to Project' });
		await goToProject.waitFor({ state: 'visible', timeout: 120000 });
		await goToProject.click();

		// Navigate to import page
		const importUrl = page.url().replace('/overview', '/budget/import');
		await page.goto(importUrl);

		// Upload the same file twice to test duplicate detection
		const fileContent = `Code,Description,Quantity,Unit,Unit Rate
1.2.1,Duplicate Test Item,8,each,150`;

		// First import
		const fileInput = page.locator('input[type="file"]');
		await fileInput.setInputFiles({
			name: 'duplicate-test.csv',
			mimeType: 'text/csv',
			buffer: Buffer.from(fileContent),
		});

		// Complete first import
		await expect(page.getByText('File uploaded successfully')).toBeVisible({ timeout: 10000 });
		await page.click('button:has-text("Next")'); // Column mapping
		await page.click('button:has-text("Next")'); // Row classification
		await page.click('button:has-text("Next")'); // WBS mapping
		await page.click('button:has-text("Next")'); // Review

		await page.click('button:has-text("Import")');
		await expect(page.getByText(/Import completed successfully/)).toBeVisible({ timeout: 30000 });

		// Start second import with same file
		await page.goto(importUrl);
		await fileInput.setInputFiles({
			name: 'duplicate-test.csv',
			mimeType: 'text/csv',
			buffer: Buffer.from(fileContent),
		});

		// Navigate to review step
		await page.click('button:has-text("Next")'); // Column mapping
		await page.click('button:has-text("Next")'); // Row classification
		await page.click('button:has-text("Next")'); // WBS mapping
		await page.click('button:has-text("Next")'); // Review

		// Verify duplicate detection banner is shown
		await expect(page.getByText(/duplicate import detected/i)).toBeVisible();
		await expect(page.getByText('duplicate-test.csv')).toBeVisible();

		// Verify reuse option is available
		await expect(page.getByRole('button', { name: /Reuse/ })).toBeVisible();
	});

	test('version comparison shows detailed diff', async ({ page }) => {
		// Setup project with versions
		await page.goto('/admin-tools');
		await page.locator('#riba-confirm-checkbox').click();
		await page.click('button:has-text("Generate RIBA Demo Project Data")');

		const goToProject = page.getByRole('button', { name: 'Go to Project' });
		await goToProject.waitFor({ state: 'visible', timeout: 120000 });
		await goToProject.click();

		// Navigate to budget and open version history
		const budgetUrl = page.url().replace('/overview', '/budget');
		await page.goto(budgetUrl);

		await page.getByRole('button', { name: 'Version History' }).click();
		await expect(page.getByText('Budget Version History')).toBeVisible();

		// Find and compare versions
		const compareButton = page.getByRole('button', { name: 'Compare to Active' }).first();
		if (await compareButton.isVisible()) {
			await compareButton.click();

			// Wait for comparison to load
			await page.waitForTimeout(3000);

			// Verify diff interface is shown (exact implementation depends on UI)
			// This is a placeholder - actual assertions would depend on the diff UI implementation
			await expect(page.locator('body')).toBeVisible();
		}
	});
});
