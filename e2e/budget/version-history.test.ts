import { expect, test } from '@playwright/test';

test.describe('Budget Version Manager - Version History dialog', () => {
	test('signs in, generates demo project, and opens dialog', async ({ page }) => {
		// Sign in with seeded local user
		await page.goto('/auth/signin');
		await page.fill('input[name="email"]', '<EMAIL>');
		await page.fill('input[name="password"]', 'testtest');
		await page.click('button:has-text("Sign In")');

		// Navigate to admin tools and generate demo project data
		await page.goto('/admin-tools');
		await expect(page.locator('h1:has-text("Admin Tools")')).toBeVisible();

		// Fill total budget and confirm
		const budgetInput = page.locator('input[name="totalBudget"]');
		if (await budgetInput.count()) {
			await budgetInput.fill('1000000000');
		}
		await page.locator('#riba-confirm-checkbox').click();
		await page.click('button:has-text("Generate RIBA Demo Project Data")');

		// Click the toast action to go to the generated project
		const goToProject = page.getByRole('button', { name: 'Go to Project' });
		await goToProject.waitFor({ state: 'visible', timeout: 120000 });
		await goToProject.click();

		// Landed on project overview; navigate to budget page
		await expect(page).toHaveURL(/\/org\/.+\/projects\/.+\/overview/);
		const budgetUrl = page.url().replace('/overview', '/budget');
		await page.goto(budgetUrl);

		// Open Version History dialog
		await page.getByRole('button', { name: 'Version History' }).click();
		await expect(page.getByText('Budget Version History')).toBeVisible();

		// Verify the dialog opened successfully
		await expect(page.getByText('Budget Version History')).toBeVisible();

		// Wait a moment for the dialog to fully load
		await page.waitForTimeout(500);

		// Check if there are any table rows (versions) - should have demo data
		const rows = page.locator('table tbody tr');
		const rowCount = await rows.count();

		// The test passes if we can open the dialog and see version data
		expect(rowCount).toBeGreaterThan(0);
	});
});
