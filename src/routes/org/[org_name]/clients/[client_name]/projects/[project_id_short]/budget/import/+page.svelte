<script lang="ts">
	import { But<PERSON> } from '$lib/components/ui/button';
	import * as Alert from '$lib/components/ui/alert';
	import {
		displaySize,
		FileDropZone,
		MEGABYTE,
		type FileDropZoneProps,
	} from '$lib/components/ui/file-drop-zone';
	import {
		Table,
		TableBody,
		TableCell,
		// TableHead,
		// TableHeader,
		TableRow,
	} from '$lib/components/ui/table';
	import { X, TriangleAlert, Info } from '@lucide/svelte';
	import ArrowSquareOut from 'phosphor-svelte/lib/ArrowSquareOut';
	import { toast } from 'svelte-sonner';
	import { page } from '$app/state';
	import { goto } from '$app/navigation';
	import { parseExcelFile, getExcelPreviewData, type ExcelParseResult } from '$lib/excel_parser';
	import type {
		ColumnMapping,
		ClassifiedRow,
		transformToImportData,
		ExcelRow,
		ImportItemJson,
		ImportItem,
	} from '$lib/budget_import_utils';
	import ColumnClassifier from '$lib/components/budget-import/ColumnClassifier.svelte';
	import RowClassifier from '$lib/components/budget-import/RowClassifier.svelte';
	import ImportSummary from '$lib/components/budget-import/ImportSummary.svelte';
	import posthog from 'posthog-js';

	interface BudgetVersionDiffSummary {
		version_a: string;
		version_b: string;
		total_cost_a: number;
		total_cost_b: number;
		total_cost_delta: number;
	}

	interface BudgetVersionDiffItem {
		wbs_library_item_id: string;
		quantity?: number;
		unit?: string;
		material_rate?: number;
		labor_rate?: number;
		productivity_per_hour?: number;
		unit_rate_manual_override?: number;
		unit_rate?: number;
		factor?: number;
		remarks?: string;
		cost_certainty?: string;
		design_certainty?: string;
		diff?: Record<string, unknown>;
	}

	interface BudgetVersionDiff {
		added: BudgetVersionDiffItem[];
		removed: BudgetVersionDiffItem[];
		changed: BudgetVersionDiffItem[];
		summary: BudgetVersionDiffSummary;
	}

	interface TelemetryData {
		project_id: string;
		budget_import_id: string;
		new_version_id: string;
		pre_version_id: string;
		reused: boolean;
		inserted_count: number;
		wbs_created_count: number;
		duration_ms: number;
		diff_summary?: BudgetVersionDiffSummary;
		items_added?: number;
		items_removed?: number;
		items_changed?: number;
	}

	const { data } = $props();

	const PREVIEW_ROWS = 50;

	// Wizard state
	let currentStep = $state(parseInt(page.url.searchParams.get('step') || '1'));
	let uploadedFile = $state<File | null>(null);
	let fileBuffer = $state<ArrayBuffer | null>(null);
	let parseResult = $state<ExcelParseResult | null>(null);
	let previewData = $state<ExcelRow[]>([]);
	let columnMapping = $state<ColumnMapping>({});
	let classifiedRows = $state<ClassifiedRow[]>([]);
	let isImporting = $state(false);

	// Load state from localStorage on mount
	// $effect(() => {
	// 	const saved = localStorage.getItem('budget_import_state');
	// 	if (saved) {
	// 		try {
	// 			const state = JSON.parse(saved);
	// 			if (state.step) currentStep = state.step;
	// 			// Note: File buffer can't be saved to localStorage due to size
	// 		} catch (e) {
	// 			console.warn('Failed to load import state:', e);
	// 		}
	// 	}
	// });

	// // Save state to localStorage when it changes
	// $effect(() => {
	// 	const state = {
	// 		step: currentStep,
	// 		columnMapping,
	// 		// Don't save file buffer or large data structures
	// 	};
	// 	localStorage.setItem('budget_import_state', JSON.stringify(state));
	// });

	$effect(() => {
		if (currentStep > 1 && !uploadedFile) {
			currentStep = 1;
			updateUrl();
		}
	});

	// Update URL when step changes
	function updateUrl() {
		const url = new URL(page.url);
		url.searchParams.set('step', currentStep.toString());
		goto(url.toString(), { replaceState: true, noScroll: true });
	}

	const steps = [
		{ number: 1, title: 'Upload File', description: 'Select Excel file to import' },
		{ number: 2, title: 'Preview Data', description: 'Review raw data from file' },
		{ number: 3, title: 'Map Columns', description: 'Identify column types' },
		{ number: 4, title: 'Classify Rows', description: 'Categorize and organize data' },
		{ number: 5, title: 'Import', description: 'Review and import to database' },
	];

	const onUpload: FileDropZoneProps['onUpload'] = async (uploadedFiles) => {
		if (uploadedFiles.length === 0) return;

		const file = uploadedFiles[0];
		uploadedFile = file;

		try {
			fileBuffer = await file.arrayBuffer();
			parseResult = parseExcelFile(fileBuffer);
			previewData = getExcelPreviewData(fileBuffer, 1_000);

			toast.success('File uploaded successfully');

			currentStep = 2;
			updateUrl();
		} catch (error) {
			console.error('Failed to parse file:', error);
			toast.error('Failed to parse Excel file', {
				description: error instanceof Error ? error.message : 'Unknown error',
			});
		}
	};

	const onFileRejected: FileDropZoneProps['onFileRejected'] = async ({ reason, file }) => {
		console.log({ reason, file });
		toast.error(`${file.name} failed to upload!`, { description: reason });
	};

	function removeFile() {
		uploadedFile = null;
		fileBuffer = null;
		parseResult = null;
		previewData = [];
		currentStep = 1;
	}

	function nextStep() {
		if (currentStep < steps.length) {
			currentStep += 1;

			updateUrl();
		}
	}

	function prevStep() {
		if (currentStep > 1) {
			currentStep -= 1;

			updateUrl();
		}
	}

	function handleColumnMappingChange(mapping: ColumnMapping) {
		columnMapping = mapping;
	}

	function handleClassifiedRowsChange(rows: ClassifiedRow[]) {
		classifiedRows = rows;
	}

	async function handleImport(importData: ReturnType<typeof transformToImportData>) {
		isImporting = true;

		try {
			// Generate a hash for the import to enable idempotency
			const sourceHash = await generateImportHash(uploadedFile!.name, importData.items);

			const response = await data.supabase.rpc('apply_budget_import', {
				p_project_id: data.project.project_id,
				p_source_filename: uploadedFile!.name,
				p_source_hash: sourceHash,
				p_items: importData.items as ImportItemJson[],
			});

			if (response.error) {
				throw new Error(response.error.message);
			}

			const result = response.data as {
				reused: boolean;
				budget_import_id: string;
				new_version_id: string;
				pre_version_id: string;
				inserted_count: number;
				wbs_created_count: number;
				duration_ms: number;
			};

			// Activate the imported/reused version so reads reflect the new data
			const activate = await data.supabase.rpc('activate_budget_version', {
				p_version_id: result.new_version_id,
				p_reason: result.reused ? 'Activate reused import version' : 'Activate new import version',
			});
			if (activate.error) {
				toast.error('Import activation failed', {
					description: activate.error.message,
				});
			} else {
				// Optionally show a diff summary for new imports
				let diffSummary = '';
				if (!result.reused && result.pre_version_id) {
					try {
						const { data: diffData } = await data.supabase.rpc('diff_budget_versions', {
							p_version_a: result.pre_version_id,
							p_version_b: result.new_version_id,
						});
						const diffResult = diffData as unknown as BudgetVersionDiff;
						if (diffResult?.summary) {
							const costDelta = diffResult.summary.total_cost_delta || 0;
							const deltaFormatted =
								costDelta >= 0 ? `+${costDelta.toLocaleString()}` : costDelta.toLocaleString();
							diffSummary = ` (Cost change: $${deltaFormatted})`;
						}
					} catch {
						// Ignore diff errors
					}
				}

				if (result.reused) {
					toast.success('Budget import reused; version activated.', {
						description: `Found prior import with same content. Activated existing version.`,
						action: {
							label: 'View History',
							onClick: () => {
								goto(
									`/org/${encodeURIComponent(page.params.org_name!)}/clients/${encodeURIComponent(page.params.client_name!)}/projects/${encodeURIComponent(data.project_id_short)}/budget/versions`,
								);
							},
						},
					});
				} else {
					toast.success('Budget imported and activated!', {
						description: `Imported ${result.inserted_count} items, created ${result.wbs_created_count} WBS items in ${result.duration_ms}ms${diffSummary}`,
						action: {
							label: 'View Diff',
							onClick: () => {
								goto(
									`/org/${encodeURIComponent(page.params.org_name!)}/clients/${encodeURIComponent(page.params.client_name!)}/projects/${encodeURIComponent(data.project_id_short)}/budget/versions?compare=${result.pre_version_id}&with=${result.new_version_id}`,
								);
							},
						},
					});
				}
			}

			try {
				// Enhanced telemetry with diff information
				const telemetryData: TelemetryData = {
					project_id: data.project.project_id,
					budget_import_id: result.budget_import_id,
					new_version_id: result.new_version_id,
					pre_version_id: result.pre_version_id,
					reused: result.reused,
					inserted_count: result.inserted_count,
					wbs_created_count: result.wbs_created_count,
					duration_ms: result.duration_ms,
				};

				// Add diff data if available
				if (!result.reused && result.pre_version_id) {
					try {
						const { data: diffData } = await data.supabase.rpc('diff_budget_versions', {
							p_version_a: result.pre_version_id,
							p_version_b: result.new_version_id,
						});
						const diffResult = diffData as unknown as BudgetVersionDiff;
						if (diffResult) {
							telemetryData.diff_summary = diffResult.summary;
							telemetryData.items_added = Array.isArray(diffResult.added)
								? diffResult.added.length
								: 0;
							telemetryData.items_removed = Array.isArray(diffResult.removed)
								? diffResult.removed.length
								: 0;
							telemetryData.items_changed = Array.isArray(diffResult.changed)
								? diffResult.changed.length
								: 0;
						}
					} catch {
						// Ignore diff errors in telemetry
					}
				}

				posthog.capture('budget_imported', telemetryData);
			} catch (_e) {
				// Ignore
			}

			// Clear state and redirect
			localStorage.removeItem('budget_import_state');
			goto(
				`/org/${encodeURIComponent(page.params.org_name!)}/clients/${encodeURIComponent(page.params.client_name!)}/projects/${encodeURIComponent(data.project_id_short)}/budget`,
			);
		} catch (error) {
			console.error('Import failed:', error);
			toast.error('Import failed', {
				description: error instanceof Error ? error.message : 'Unknown error',
			});
			try {
				posthog.capture('budget_import_failed', {
					project_id: data.project.project_id,
					reason: error instanceof Error ? error.message : String(error),
				});
			} catch (_e) {
				// Ignore
			}
		} finally {
			isImporting = false;
		}
	}

	// Generate a hash for import idempotency
	async function generateImportHash(filename: string, items: ImportItem[]): Promise<string> {
		const content = JSON.stringify({ filename, items });
		const encoder = new TextEncoder();
		const data = encoder.encode(content);
		const hashBuffer = await crypto.subtle.digest('SHA-256', data);
		const hashArray = Array.from(new Uint8Array(hashBuffer));
		return hashArray.map((b) => b.toString(16).padStart(2, '0')).join('');
	}
</script>

<div class="px-4 py-8">
	<div class="mb-8">
		<h1 class="mb-2 text-3xl font-bold">Import Budget</h1>
		<p class="text-muted-foreground">
			Upload a CostX Excel export to create your budget items with hierarchical categorization.
		</p>
	</div>

	<!-- Progress Steps -->
	<div class="mb-8">
		<div class="flex items-center justify-between">
			{#each steps as step (step.number)}
				<div class="flex items-center">
					<div
						class="flex h-8 w-8 items-center justify-center rounded-full border-2 text-sm font-medium"
						class:bg-primary={currentStep >= step.number}
						class:text-primary-foreground={currentStep >= step.number}
						class:border-primary={currentStep >= step.number}
						class:border-muted={currentStep < step.number}
						class:text-muted-foreground={currentStep < step.number}
					>
						{step.number}
					</div>
					<div class="ml-2 hidden sm:block">
						<div class="text-sm font-medium">{step.title}</div>
						<div class="text-muted-foreground text-xs">{step.description}</div>
					</div>
				</div>
				{#if step.number < steps.length}
					<div
						class="mx-4 h-0.5 flex-1"
						class:bg-primary={currentStep > step.number}
						class:bg-muted={currentStep <= step.number}
					></div>
				{/if}
			{/each}
		</div>
	</div>

	<!-- Step Content -->
	<div class="bg-card rounded-lg border p-6">
		{#if currentStep === 1}
			<!-- Step 1: File Upload -->
			<div class="space-y-6">
				<div>
					<h2 class="mb-2 text-xl font-semibold">Step 1: Upload Excel File</h2>
					<p class="text-muted-foreground">
						Select a CostX Excel export file (.xlsx) to import budget data.
					</p>
					<p class="text-muted-foreground mt-2 text-sm">
						Need help exporting from CostX?
						<a
							href="/org/{encodeURIComponent(page.params.org_name!)}/clients/{encodeURIComponent(
								page.params.client_name!,
							)}/projects/{encodeURIComponent(
								data.project_id_short,
							)}/budget/import/costx-instructions"
							target="_blank"
							class="hover:bg-accent ml-1 inline-flex items-center gap-1 rounded p-1 font-bold underline hover:text-black"
						>
							We have export instructions <ArrowSquareOut />
						</a>
					</p>
				</div>

				<FileDropZone
					{onUpload}
					{onFileRejected}
					maxFileSize={10 * MEGABYTE}
					accept=".xlsx, application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"
					fileCount={uploadedFile ? 1 : 0}
				/>

				{#if uploadedFile}
					<div class="flex items-center justify-between rounded border p-3">
						<div class="flex flex-col">
							<span class="font-medium">{uploadedFile.name}</span>
							<span class="text-muted-foreground text-sm">{displaySize(uploadedFile.size)}</span>
						</div>
						<Button variant="outline" size="icon" onclick={removeFile}>
							<X class="h-4 w-4" />
						</Button>
					</div>
				{/if}
			</div>
		{:else if currentStep === 2}
			<!-- Step 2: Preview Data -->
			<div class="space-y-6">
				<div>
					<h2 class="mb-2 text-xl font-semibold">Step 2: Preview Data</h2>
					<p class="text-muted-foreground">Review the raw data extracted from your Excel file.</p>
				</div>

				{#if parseResult?.hasMultipleSheets}
					<Alert.Root class="bg-blue-200">
						<Info class="h-4 w-4" />
						<Alert.Title>
							Multiple sheets detected. Only the first sheet will be imported.
						</Alert.Title>
					</Alert.Root>
				{/if}

				{#if parseResult?.hasYellowFill}
					<Alert.Root class="bg-amber-200">
						<TriangleAlert class="h-4 w-4" />
						<Alert.Title class="">Yellow highlighted cells detected.</Alert.Title>
						<Alert.Description class="">
							CostX is alerting us that there are additional levels of detail in the estimate or
							that some cost data is entered at a higher level in the WBS hierarchy. Consider
							exporting more levels of data from CostX to include all available data.
						</Alert.Description>
					</Alert.Root>
				{/if}

				{#if previewData.length > 0}
					<div class="overflow-hidden rounded-lg border">
						<Table>
							<!-- Should we show column numbers? Or maybe convert to fromCharCode 1->A? -->
							<!-- <TableHeader>
								<TableRow>
									{#each Object.keys(previewData[0]) as key (key)}
										<TableHead>{String.fromCharCode(65 + Number(key))}</TableHead>
									{/each}
								</TableRow>
							</TableHeader> -->

							<TableBody>
								{#each previewData.slice(0, PREVIEW_ROWS) as row, index (index)}
									<TableRow>
										{#each Object.keys(previewData[0]) as key (key)}
											<TableCell class="max-w-32 truncate not-last:border-r"
												>{row[key] || ''}</TableCell
											>
										{/each}
									</TableRow>
								{/each}
							</TableBody>
						</Table>
					</div>
					{#if Math.min(previewData.length, PREVIEW_ROWS) === (parseResult?.rows?.length || 0) + (parseResult?.headerRowIndex ?? 0) + 1}
						<p class="text-muted-foreground text-sm">
							Showing all {(parseResult?.rows?.length || 0) +
								(parseResult?.headerRowIndex ?? 0) +
								1} rows found.
						</p>
					{:else}
						<p class="text-muted-foreground text-sm">
							Showing first {Math.min(previewData.length, PREVIEW_ROWS)} rows. Found {(parseResult
								?.rows?.length || 0) +
								(parseResult?.headerRowIndex ?? 0) +
								1} total rows.
						</p>
					{/if}
				{/if}

				<div class="flex justify-between">
					<Button variant="outline" onclick={prevStep}>Back</Button>
					<Button onclick={nextStep}>Next: Map Columns</Button>
				</div>
			</div>
		{:else if currentStep === 3}
			<!-- Step 3: Column Mapping -->
			{#if parseResult?.headers}
				<ColumnClassifier
					headers={parseResult.headers}
					sampleRows={parseResult.rows.slice(0, 10)}
					rawPreviewData={previewData.slice(parseResult?.headerRowIndex ?? 0)}
					onMappingChange={handleColumnMappingChange}
					onNext={nextStep}
					onBack={prevStep}
				/>
			{/if}
		{:else if currentStep === 4}
			<!-- Step 4: Row Classification -->
			{#if parseResult?.rows}
				<RowClassifier
					rows={parseResult.rows}
					{columnMapping}
					onClassifiedRowsChange={handleClassifiedRowsChange}
					onNext={nextStep}
					onBack={prevStep}
				/>
			{/if}
		{:else if currentStep === 5}
			<!-- Step 5: Import Summary -->
			<ImportSummary
				{classifiedRows}
				{columnMapping}
				projectId={data.project.project_id}
				onImport={handleImport}
				onBack={prevStep}
				{isImporting}
				supabase={data.supabase}
			/>
		{/if}
	</div>
</div>
