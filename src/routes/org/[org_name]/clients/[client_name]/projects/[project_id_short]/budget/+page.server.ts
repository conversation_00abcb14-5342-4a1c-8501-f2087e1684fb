import { redirect } from 'sveltekit-flash-message/server';
import { requireProject } from '$lib/server/auth';
import { message, superValidate } from 'sveltekit-superforms/server';
import { zod4 as zod } from 'sveltekit-superforms/adapters';
import { type Actions, fail } from '@sveltejs/kit';
import { upsertBudgetLineItem } from '$lib/project_utils';
import type { PageServerLoad } from './$types';
import { budgetItemSchema, projectUUID } from '$lib/schemas/project';
import { wbsLibraryItemComparator } from '$lib/budget_utils';
import type { Tables } from '$lib/database.types';

export const load: PageServerLoad = async ({ locals, cookies, depends }) => {
	depends('project:budget');

	const { supabase } = locals;
	const { org_name, client_name, project_id_short } = requireProject();

	const project_id = projectUUID(project_id_short);

	// Fetch the project data
	const { data: projectData, error: projectError } = await supabase
		.from('project')
		.select(
			`*,
				wbs_library:wbs_library(wbs_library_item(*))`,
		)
		.eq('project_id', project_id)
		.limit(1)
		.maybeSingle();

	if (projectError || !projectData) {
		locals.log.error({ msg: 'Error fetching project', projectError });
		return redirect(
			`/org/${encodeURIComponent(org_name)}/clients/${encodeURIComponent(client_name)}`,
			{ type: 'error', message: 'Project not found' },
			cookies,
		);
	}

	// Extract related data returned from the join
	const allWbsItems =
		projectData.wbs_library?.wbs_library_item.sort(wbsLibraryItemComparator) || [];

	// Fetch budget items from the active budget version
	const { data: rawCurrentItems, error: budgetError } = await supabase.rpc(
		'get_active_budget_version_items' as any,
		{
			p_project_id: project_id,
		},
	);

	if (budgetError) {
		locals.log.error({ msg: 'Error fetching active budget version items', budgetError });
		// Continue with empty budget items rather than failing completely
	}

	// Type the rawCurrentItems to match the expected structure
	const typedRawCurrentItems = (rawCurrentItems || []) as Array<{
		budget_line_item_id: string;
		project_id: string;
		wbs_library_item_id: string;
		quantity: number;
		unit: string;
		material_rate: number;
		labor_rate: number;
		productivity_per_hour: number;
		unit_rate_manual_override: boolean;
		unit_rate: number;
		factor: number;
		remarks: string;
		cost_certainty: number;
		design_certainty: number;
		created_at: string;
		updated_at: string;
	}>;

	const wbsItems =
		allWbsItems?.map((i) => ({
			label: `${i.code}: ${i.description}`,
			value: i.wbs_library_item_id,
		})) || [];

	// Create the form with the budget item schema
	const form = await superValidate(zod(budgetItemSchema));

	// is this construction stage?
	const { data: isConstructionStage } = await supabase.rpc('is_construction_stage', {
		project_id_param: projectData.project_id,
	});
	// if yes, get risk register data
	let riskRegisterData: Tables<'risk_register'>[] = [];
	if (isConstructionStage) {
		const { data: riskData } = await supabase
			.from('risk_register')
			.select('*')
			.eq('project_id', projectData.project_id);
		riskRegisterData = riskData || [];
	}

	return {
		project: projectData,
		wbsItems,
		allWbsItems: allWbsItems || [],
		rawCurrentItems: typedRawCurrentItems,
		form,
		riskRegisterData,
	};
};

export const actions: Actions = {
	// Upsert a budget line item
	upsertBudgetItem: async ({ request, locals }) => {
		const { supabase } = locals;

		const form = await superValidate(request, zod(budgetItemSchema));
		if (!form.valid) {
			return fail(400, { form });
		}

		// Check permissions
		const { data: canEdit } = await supabase.rpc('can_modify_project', {
			project_id_param: form.data.project_id,
		});

		if (!canEdit) {
			return message(
				form,
				{ type: 'error', text: 'You do not have permission to edit this project' },
				{ status: 403 },
			);
		}

		// Check if current stage is construction stage - don't allow updates during construction
		const { data: isConstructionStage } = await supabase.rpc('is_construction_stage', {
			project_id_param: form.data.project_id,
		});

		if (isConstructionStage) {
			return message(
				form,
				{
					type: 'error',
					text: 'Budget modifications are not allowed during the construction stage',
				},
				{ status: 403 },
			);
		}

		// Update the budget line item
		try {
			const id = await upsertBudgetLineItem(supabase, form.data);
			locals.log.info({ msg: 'Budget line item upserted', id });
			return message(form, { type: 'success', text: 'Budget line item saved successfully' });
		} catch (error) {
			locals.log.error({ msg: 'Error upserting budget line item', error });
			return message(
				form,
				{ type: 'error', text: 'Error saving budget line item' },
				{ status: 500 },
			);
		}
	},

	// Delete a budget line item
	deleteBudgetItem: async ({ request, locals }) => {
		const { supabase } = locals;

		const form = await superValidate(request, zod(budgetItemSchema));
		if (!form.valid) {
			return fail(400, { form });
		}

		const { project_id: projectId, budget_line_item_id: budgetLineItemId } = form.data;

		if (!projectId || !budgetLineItemId) {
			return fail(400, { success: false, message: 'Missing required fields' });
		}

		// Check permissions
		const { data: canEdit } = await supabase.rpc('can_modify_project', {
			project_id_param: projectId,
		});

		if (!canEdit) {
			return fail(403, {
				success: false,
				message: 'You do not have permission to edit this project',
			});
		}

		// Check if current stage is construction stage - don't allow deletions during construction
		const { data: isConstructionStage } = await supabase.rpc('is_construction_stage', {
			project_id_param: projectId,
		});

		if (isConstructionStage) {
			return fail(403, {
				success: false,
				message: 'Budget modifications are not allowed during the construction stage',
			});
		}

		// Delete the specific budget line item by its ID
		try {
			const { error } = await supabase
				.from('budget_line_item_current')
				.delete()
				.eq('budget_line_item_id', budgetLineItemId);

			if (error) throw error;

			return {
				success: true,
				message: { type: 'success', text: 'Budget line item deleted successfully' },
			};
		} catch (error) {
			locals.log.error({ msg: 'Error deleting budget line item', error });
			return fail(500, { success: false, message: 'Error deleting budget line item' });
		}
	},
};
