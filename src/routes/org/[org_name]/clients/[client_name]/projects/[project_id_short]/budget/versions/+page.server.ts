import { error } from '@sveltejs/kit';
import type { PageServerLoad } from './$types';
import { projectUUID } from '$lib/schemas/project';

export const load: PageServerLoad = async ({ params, url, locals: { supabase } }) => {
	const { org_name, client_name, project_id_short } = params;
	const compareVersionId = url.searchParams.get('compare');
	const withVersionId = url.searchParams.get('with');

	if (!compareVersionId || !withVersionId) {
		throw error(400, 'Both compare and with parameters are required');
	}

	// Convert project_id_short to UUID and get project info
	const project_id = projectUUID(project_id_short);

	const { data: project, error: projectError } = await supabase
		.from('project')
		.select('project_id, name')
		.eq('project_id', project_id)
		.single();

	if (projectError || !project) {
		throw error(404, 'Project not found');
	}

	// Get version details for both versions
	const { data: versions, error: versionsError } = await supabase.rpc('list_budget_versions', {
		p_project_id: project.project_id,
		p_limit: 100,
	});

	if (versionsError) {
		throw error(500, `Failed to load versions: ${versionsError.message}`);
	}

	const compareVersion = versions?.find((v: any) => v.budget_version_id === compareVersionId);
	const withVersion = versions?.find((v: any) => v.budget_version_id === withVersionId);

	if (!compareVersion || !withVersion) {
		throw error(404, 'One or both versions not found');
	}

	// Get the diff between the two versions
	const { data: diffData, error: diffError } = await supabase.rpc('diff_budget_versions', {
		p_version_a: compareVersionId,
		p_version_b: withVersionId,
	});

	if (diffError) {
		throw error(500, `Failed to compute diff: ${diffError.message}`);
	}

	// Get import records if they exist for these versions
	const { data: imports, error: importsError } = await supabase
		.from('budget_import')
		.select('*')
		.eq('project_id', project.project_id)
		.in('new_version_id', [compareVersionId, withVersionId]);

	if (importsError) {
		console.warn('Failed to load import records:', importsError.message);
	}

	return {
		project: {
			...project,
			project_id_short,
		},
		compareVersion,
		withVersion,
		diffData,
		imports: imports || [],
		org_name,
		client_name,
	};
};
