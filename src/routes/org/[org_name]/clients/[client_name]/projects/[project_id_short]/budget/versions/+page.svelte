<script lang="ts">
	import { But<PERSON> } from '$lib/components/ui/button';
	import { Badge } from '$lib/components/ui/badge';
	import { <PERSON>, CardContent, CardHeader, CardTitle } from '$lib/components/ui/card';
	import {
		Table,
		TableBody,
		TableCell,
		TableHead,
		TableHeader,
		TableRow,
	} from '$lib/components/ui/table';
	import {
		ArrowLeft,
		FileText,
		Upload,
		User,
		TrendingUp,
		TrendingDown,
		Minus,
	} from '@lucide/svelte';
	import { formatCurrency, formatDate } from '$lib/utils';
	import type { PageData } from './$types';
	import type { Database } from '$lib/database.types';

	type BudgetVersionKind = Database['public']['Enums']['budget_version_kind'];
	type BudgetImportRow = Database['public']['Tables']['budget_import']['Row'];

	interface BudgetVersion {
		budget_version_id: string;
		label: string | null;
		kind: BudgetVersionKind;
		is_active: boolean;
		item_count: number;
		total_cost: number;
		created_at: string;
	}

	interface BudgetVersionDiffSummary {
		version_a: string;
		version_b: string;
		total_cost_a: number;
		total_cost_b: number;
		total_cost_delta: number;
	}

	interface BudgetVersionDiffItem {
		wbs_library_item_id: string;
		code?: string;
		description?: string;
		quantity?: number;
		unit?: string;
		material_rate?: number;
		labor_rate?: number;
		productivity_per_hour?: number;
		unit_rate_manual_override?: number;
		unit_rate?: number;
		factor?: number;
		remarks?: string;
		cost_certainty?: string;
		design_certainty?: string;
		diff?: Record<
			string,
			{ from: string | number | boolean | null; to: string | number | boolean | null }
		>;
	}

	interface BudgetVersionDiff {
		added: BudgetVersionDiffItem[];
		removed: BudgetVersionDiffItem[];
		changed: BudgetVersionDiffItem[];
		summary: BudgetVersionDiffSummary;
	}

	let { data }: { data: PageData } = $props();

	const diffData = data.diffData as unknown as BudgetVersionDiff;

	function getVersionLabel(version: BudgetVersion): string {
		if (version.label) return version.label;

		switch (version.kind) {
			case 'stage':
				return 'Stage Version';
			case 'import':
				return 'Import Version';
			case 'manual':
				return 'Manual Version';
			case 'system':
				return 'System Version';
			default:
				return 'Unknown Version';
		}
	}

	function getVersionBadgeVariant(
		version: BudgetVersion,
	): 'default' | 'secondary' | 'destructive' | 'outline' {
		if (version.is_active) return 'default';

		switch (version.kind) {
			case 'import':
				return 'secondary';
			case 'manual':
				return 'outline';
			default:
				return 'outline';
		}
	}

	function getVersionIcon(kind: string) {
		switch (kind) {
			case 'import':
				return Upload;
			case 'stage':
				return User;
			default:
				return FileText;
		}
	}

	function formatDiffValue(value: string | number | boolean | null): string {
		if (typeof value === 'number') {
			return value.toLocaleString();
		}
		return String(value || '');
	}

	function calculateItemCost(item: BudgetVersionDiffItem): number {
		const quantity = item.quantity || 0;
		const unitRate = item.unit_rate || 0;
		const factor = item.factor || 1;
		return quantity * unitRate * factor;
	}

	const budgetUrl = `/org/${encodeURIComponent(data.org_name)}/clients/${encodeURIComponent(data.client_name)}/projects/${encodeURIComponent(data.project.project_id_short)}/budget`;

	// Helper to get import record for a version
	function getImportRecord(versionId: string) {
		return data.imports.find((imp: BudgetImportRow) => imp.new_version_id === versionId);
	}
</script>

<svelte:head>
	<title>Budget Version Comparison - {data.project.name}</title>
</svelte:head>

<div class="container mx-auto space-y-6 p-6">
	<!-- Header with navigation -->
	<div class="flex items-center gap-4">
		<Button variant="outline" size="sm" href={budgetUrl}>
			<ArrowLeft class="mr-2 h-4 w-4" />
			Back to Budget
		</Button>
		<div>
			<h1 class="text-2xl font-bold">Budget Version Comparison</h1>
			<p class="text-muted-foreground">{data.project.name}</p>
		</div>
	</div>

	<!-- Version Details -->
	<div class="grid grid-cols-1 gap-6 md:grid-cols-2">
		<!-- Compare Version (A) -->
		<Card>
			<CardHeader>
				<CardTitle class="flex items-center gap-2">
					{@const Icon = getVersionIcon(data.compareVersion.kind)}
					<Icon class="h-5 w-5" />
					{getVersionLabel(data.compareVersion)}
				</CardTitle>
			</CardHeader>
			<CardContent class="space-y-3">
				{@const importRecord = getImportRecord(data.compareVersion.budget_version_id)}
				<div class="flex items-center gap-2">
					<Badge variant={getVersionBadgeVariant(data.compareVersion)}>
						{data.compareVersion.is_active ? 'Active' : data.compareVersion.kind}
					</Badge>
					{#if importRecord?.is_undone}
						<Badge variant="destructive">Undone</Badge>
					{/if}
				</div>
				{#if importRecord}
					<div class="text-muted-foreground text-sm">
						<FileText class="mr-1 inline h-3 w-3" />
						{importRecord.source_filename}
					</div>
				{/if}
				<div class="space-y-1">
					<div class="text-sm">
						<span class="font-medium">Items:</span>
						{data.compareVersion.item_count.toLocaleString()}
					</div>
					<div class="text-sm">
						<span class="font-medium">Total Cost:</span>
						{formatCurrency(data.compareVersion.total_cost)}
					</div>
					<div class="text-sm">
						<span class="font-medium">Created:</span>
						{formatDate(data.compareVersion.created_at, 'MMM dd, yyyy')}
					</div>
				</div>
			</CardContent>
		</Card>

		<!-- With Version (B) -->
		<Card>
			<CardHeader>
				<CardTitle class="flex items-center gap-2">
					{@const Icon = getVersionIcon(data.withVersion.kind)}
					<Icon class="h-5 w-5" />
					{getVersionLabel(data.withVersion)}
				</CardTitle>
			</CardHeader>
			<CardContent class="space-y-3">
				{@const importRecord = getImportRecord(data.withVersion.budget_version_id)}
				<div class="flex items-center gap-2">
					<Badge variant={getVersionBadgeVariant(data.withVersion)}>
						{data.withVersion.is_active ? 'Active' : data.withVersion.kind}
					</Badge>
					{#if importRecord?.is_undone}
						<Badge variant="destructive">Undone</Badge>
					{/if}
				</div>
				{#if importRecord}
					<div class="text-muted-foreground text-sm">
						<FileText class="mr-1 inline h-3 w-3" />
						{importRecord.source_filename}
					</div>
				{/if}
				<div class="space-y-1">
					<div class="text-sm">
						<span class="font-medium">Items:</span>
						{data.withVersion.item_count.toLocaleString()}
					</div>
					<div class="text-sm">
						<span class="font-medium">Total Cost:</span>
						{formatCurrency(data.withVersion.total_cost)}
					</div>
					<div class="text-sm">
						<span class="font-medium">Created:</span>
						{formatDate(data.withVersion.created_at, 'MMM dd, yyyy')}
					</div>
				</div>
			</CardContent>
		</Card>
	</div>

	<!-- Diff Summary -->
	{#if diffData?.summary}
		<Card>
			<CardHeader>
				<CardTitle class="flex items-center gap-2">
					{#if diffData.summary.total_cost_delta > 0}
						<TrendingUp class="h-5 w-5 text-green-600" />
					{:else if diffData.summary.total_cost_delta < 0}
						<TrendingDown class="h-5 w-5 text-red-600" />
					{:else}
						<Minus class="h-5 w-5 text-gray-600" />
					{/if}
					Comparison Summary
				</CardTitle>
			</CardHeader>
			<CardContent>
				<div class="grid grid-cols-2 gap-4 md:grid-cols-4">
					<div class="text-center">
						<div class="text-2xl font-bold text-green-600">
							{Array.isArray(diffData.added) ? diffData.added.length : 0}
						</div>
						<div class="text-muted-foreground text-sm">Added Items</div>
					</div>
					<div class="text-center">
						<div class="text-2xl font-bold text-red-600">
							{Array.isArray(diffData.removed) ? diffData.removed.length : 0}
						</div>
						<div class="text-muted-foreground text-sm">Removed Items</div>
					</div>
					<div class="text-center">
						<div class="text-2xl font-bold text-blue-600">
							{Array.isArray(diffData.changed) ? diffData.changed.length : 0}
						</div>
						<div class="text-muted-foreground text-sm">Changed Items</div>
					</div>
					<div class="text-center">
						<div
							class="text-2xl font-bold {diffData.summary.total_cost_delta >= 0
								? 'text-green-600'
								: 'text-red-600'}"
						>
							{diffData.summary.total_cost_delta >= 0 ? '+' : ''}{formatCurrency(
								diffData.summary.total_cost_delta,
							)}
						</div>
						<div class="text-muted-foreground text-sm">Cost Change</div>
					</div>
				</div>
			</CardContent>
		</Card>
	{/if}

	<!-- Detailed Changes -->
	{#if diffData}
		<!-- Added Items -->
		{#if Array.isArray(diffData.added) && diffData.added.length > 0}
			<Card>
				<CardHeader>
					<CardTitle class="flex items-center gap-2 text-green-600">
						<TrendingUp class="h-5 w-5" />
						Added Items ({diffData.added.length})
					</CardTitle>
				</CardHeader>
				<CardContent>
					<Table>
						<TableHeader>
							<TableRow>
								<TableHead>Code</TableHead>
								<TableHead>Description</TableHead>
								<TableHead class="text-right">Quantity</TableHead>
								<TableHead>Unit</TableHead>
								<TableHead class="text-right">Unit Rate</TableHead>
								<TableHead class="text-right">Factor</TableHead>
								<TableHead class="text-right">Total Cost</TableHead>
							</TableRow>
						</TableHeader>
						<TableBody>
							{#each diffData.added as item (item.code)}
								<TableRow>
									<TableCell class="font-mono text-sm">{item.code || '—'}</TableCell>
									<TableCell>{item.description || '—'}</TableCell>
									<TableCell class="text-right">{item.quantity?.toLocaleString() || '—'}</TableCell>
									<TableCell>{item.unit || '—'}</TableCell>
									<TableCell class="text-right"
										>{item.unit_rate ? formatCurrency(item.unit_rate) : '—'}</TableCell
									>
									<TableCell class="text-right">{item.factor?.toLocaleString() || '—'}</TableCell>
									<TableCell class="text-right font-medium"
										>{formatCurrency(calculateItemCost(item))}</TableCell
									>
								</TableRow>
							{/each}
						</TableBody>
					</Table>
				</CardContent>
			</Card>
		{/if}

		<!-- Removed Items -->
		{#if Array.isArray(diffData.removed) && diffData.removed.length > 0}
			<Card>
				<CardHeader>
					<CardTitle class="flex items-center gap-2 text-red-600">
						<TrendingDown class="h-5 w-5" />
						Removed Items ({diffData.removed.length})
					</CardTitle>
				</CardHeader>
				<CardContent>
					<Table>
						<TableHeader>
							<TableRow>
								<TableHead>Code</TableHead>
								<TableHead>Description</TableHead>
								<TableHead class="text-right">Quantity</TableHead>
								<TableHead>Unit</TableHead>
								<TableHead class="text-right">Unit Rate</TableHead>
								<TableHead class="text-right">Factor</TableHead>
								<TableHead class="text-right">Total Cost</TableHead>
							</TableRow>
						</TableHeader>
						<TableBody>
							{#each diffData.removed as item (item.code)}
								<TableRow>
									<TableCell class="font-mono text-sm">{item.code || '—'}</TableCell>
									<TableCell>{item.description || '—'}</TableCell>
									<TableCell class="text-right">{item.quantity?.toLocaleString() || '—'}</TableCell>
									<TableCell>{item.unit || '—'}</TableCell>
									<TableCell class="text-right"
										>{item.unit_rate ? formatCurrency(item.unit_rate) : '—'}</TableCell
									>
									<TableCell class="text-right">{item.factor?.toLocaleString() || '—'}</TableCell>
									<TableCell class="text-right font-medium"
										>{formatCurrency(calculateItemCost(item))}</TableCell
									>
								</TableRow>
							{/each}
						</TableBody>
					</Table>
				</CardContent>
			</Card>
		{/if}

		<!-- Changed Items -->
		{#if Array.isArray(diffData.changed) && diffData.changed.length > 0}
			<Card>
				<CardHeader>
					<CardTitle class="flex items-center gap-2 text-blue-600">
						<FileText class="h-5 w-5" />
						Changed Items ({diffData.changed.length})
					</CardTitle>
				</CardHeader>
				<CardContent>
					<Table>
						<TableHeader>
							<TableRow>
								<TableHead>Code</TableHead>
								<TableHead>Description</TableHead>
								<TableHead>Field</TableHead>
								<TableHead>From</TableHead>
								<TableHead>To</TableHead>
								<TableHead class="text-right">Cost Impact</TableHead>
							</TableRow>
						</TableHeader>
						<TableBody>
							{#each diffData.changed as item (item.code)}
								{#if item.diff}
									{#each Object.entries(item.diff) as [field, change] (field)}
										<TableRow>
											<TableCell class="font-mono text-sm">{item.code || '—'}</TableCell>
											<TableCell>{item.description || '—'}</TableCell>
											<TableCell class="capitalize">{field.replace(/_/g, ' ')}</TableCell>
											<TableCell class="text-red-600">{formatDiffValue(change.from)}</TableCell>
											<TableCell class="text-green-600">{formatDiffValue(change.to)}</TableCell>
											<TableCell class="text-right">
												{#if field === 'quantity' || field === 'unit_rate' || field === 'factor'}
													{@const oldCost = calculateItemCost({ ...item, [field]: change.from })}
													{@const newCost = calculateItemCost({ ...item, [field]: change.to })}
													{@const costDiff = newCost - oldCost}
													<span class={costDiff >= 0 ? 'text-green-600' : 'text-red-600'}>
														{costDiff >= 0 ? '+' : ''}{formatCurrency(costDiff)}
													</span>
												{:else}
													—
												{/if}
											</TableCell>
										</TableRow>
									{/each}
								{/if}
							{/each}
						</TableBody>
					</Table>
				</CardContent>
			</Card>
		{/if}
	{/if}
</div>
