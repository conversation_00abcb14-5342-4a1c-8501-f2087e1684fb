import { requireProject } from '$lib/server/auth';
import { requireUser } from '$lib/server/auth';
import { redirect } from 'sveltekit-flash-message/server';
import type { PageServerLoad } from './$types';
import type { Tables } from '$lib/database.types';
import { projectUUID } from '$lib/schemas/project';

export const load = (async ({ params, locals, cookies, depends }) => {
	depends('project:budget');

	await requireUser();

	const { supabase } = locals;
	const { org_name, client_name, project_id_short } = requireProject();

	const project_id = projectUUID(project_id_short);

	// Fetch the project data
	// TODO: fix the wbs library part of the query
	const { data: projectData, error: projectError } = await supabase
		.from('project')
		.select(
			`*,
				wbs_library:wbs_library(wbs_library_item(*))`,
		)
		.eq('project_id', project_id)
		.limit(1)
		.maybeSingle();

	if (projectError || !projectData) {
		locals.log.error({ msg: 'Error fetching project:', projectError });
		return redirect(
			`/org/${encodeURIComponent(org_name)}/clients/${encodeURIComponent(client_name)}`,
			{ type: 'error', message: 'Project not found' },
			cookies,
		);
	}

	// Extract related data returned from the join
	const allWbsItems = projectData.wbs_library?.wbs_library_item || [];

	// Fetch budget items from the active budget version
	const { data: budgetVersionItems, error: budgetError } = await supabase.rpc(
		'get_active_budget_version_items' as any,
		{
			p_project_id: project_id,
		},
	);

	if (budgetError) {
		locals.log.error({ msg: 'Error fetching active budget version items', budgetError });
		// Continue with empty budget items rather than failing completely
	}

	// Type the budget version items to match the expected structure
	const typedBudgetVersionItems = (budgetVersionItems || []) as Array<{
		budget_line_item_id: string;
		project_id: string;
		wbs_library_item_id: string;
		quantity: number;
		unit: string;
		material_rate: number;
		labor_rate: number;
		productivity_per_hour: number;
		unit_rate_manual_override: boolean;
		unit_rate: number;
		factor: number;
		remarks: string;
		cost_certainty: number;
		design_certainty: number;
		created_at: string;
		updated_at: string;
	}>;

	// Map the budget version items to the expected format for overview
	const rawCurrentItems =
		typedBudgetVersionItems.map((bli) => {
			return {
				budget_snapshot_line_item_id: bli.budget_line_item_id, // Map to expected field name
				budget_snapshot_id: 'current', // Use 'current' as a pseudo snapshot ID
				wbs_library_item_id: bli.wbs_library_item_id,
				quantity: bli.quantity,
				unit_rate: bli.unit_rate,
				factor: bli.factor,
				label: 'current',
			};
		}) || [];

	// First, get all project stages for this project
	const { data: projectStages, error: stagesError } = await supabase
		.from('project_stage')
		.select('project_stage_id')
		.eq('project_id', projectData.project_id);

	if (stagesError) {
		locals.log.error({ msg: 'Error fetching project stages:', stagesError });
	}

	// Then fetch budget snapshots for those project stages
	const { data: budgetSnapshots, error: snapshotsError } = await supabase
		.from('budget_snapshot')
		.select(
			`
			budget_snapshot_id,
			project_stage_id,
			freeze_date,
			freeze_reason,
			created_by_user_id,
			created_at,
			updated_at,
			project_stage (
				name,
				stage_order,
				stage,
				project_id
			)
		`,
		)
		.in('project_stage_id', projectStages?.map((stage) => stage.project_stage_id) || [])
		.order('freeze_date', { ascending: true });

	if (snapshotsError) {
		locals.log.error({ msg: 'Error fetching budget snapshots:', snapshotsError });
		// Continue without snapshots rather than failing completely
	}

	// Fetch budget snapshot line items for all snapshots in a single query
	const snapshotsWithItems: Array<{
		snapshot: NonNullable<typeof budgetSnapshots>[number];
		budgetItems: Array<{
			budget_snapshot_line_item_id: string;
			budget_snapshot_id: string;
			wbs_library_item_id: string;
			quantity: number | null;
			unit_rate: number | null;
			factor: number | null;
			label: string;
		}>;
	}> = [];

	if (budgetSnapshots && budgetSnapshots.length > 0) {
		// Extract all snapshot IDs for the IN query
		const snapshotIds = budgetSnapshots.map((snapshot) => snapshot.budget_snapshot_id);

		// Fetch all snapshot items at once
		const { data: allSnapshotItems, error: itemsError } = await supabase
			.from('budget_snapshot_line_item')
			.select('*')
			.in('budget_snapshot_id', snapshotIds);

		if (itemsError) {
			locals.log.error({ msg: 'Error fetching snapshot line items:', itemsError });
		} else {
			// Group items by snapshot ID for efficient lookup
			const itemsBySnapshotId = new Map<string, Tables<'budget_snapshot_line_item'>[]>();
			allSnapshotItems?.forEach((item) => {
				if (!itemsBySnapshotId.has(item.budget_snapshot_id)) {
					itemsBySnapshotId.set(item.budget_snapshot_id, []);
				}
				itemsBySnapshotId.get(item.budget_snapshot_id)!.push(item);
			});

			// Build the snapshots with their items
			budgetSnapshots.forEach((snapshot) => {
				const snapshotItems = itemsBySnapshotId.get(snapshot.budget_snapshot_id) || [];
				snapshotsWithItems.push({
					snapshot: snapshot,
					budgetItems: snapshotItems.map((item: Tables<'budget_snapshot_line_item'>) => ({
						budget_snapshot_line_item_id: item.budget_snapshot_line_item_id,
						budget_snapshot_id: item.budget_snapshot_id,
						wbs_library_item_id: item.wbs_library_item_id,
						quantity: item.quantity,
						unit_rate: item.unit_rate,
						factor: item.factor,
						label: `snapshot-${snapshot.project_stage?.stage_order || 'unknown'}`,
					})),
				});
			});
		}
	}

	const wbsItems =
		allWbsItems?.map((i) => ({
			label: `${i.code}: ${i.description}`,
			value: i.wbs_library_item_id,
		})) || [];

	if (wbsItems.length === 0) {
		// This happens when the user did not finish importing a CostX budget.
		// TODO: can this happen another way?
		redirect(
			`/org/${encodeURIComponent(params.org_name)}/clients/${encodeURIComponent(client_name)}/projects/${encodeURIComponent(project_id_short)}/budget/import`,
			{ type: 'error', message: 'Finish importing your budget to setup the project.' },
			cookies,
		);
	}

	return {
		project: projectData,
		wbsItems,
		allWbsItems: allWbsItems || [],
		rawCurrentItems: rawCurrentItems || [],
		budgetSnapshots: budgetSnapshots || [],
		snapshotsWithItems: snapshotsWithItems || [],
	};
}) satisfies PageServerLoad;
