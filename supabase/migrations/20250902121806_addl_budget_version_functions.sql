set
	check_function_bodies = off;

CREATE OR REPLACE FUNCTION public.diff_active_vs_items (p_project_id uuid, p_items jsonb) RETURNS jsonb LANGUAGE plpgsql
SET
	search_path TO '' AS $function$
DECLARE
    v_user_id uuid;
    v_active_version_id uuid;
    v_wbs_library_id uuid;
    v_client_id uuid;
    result jsonb := '{}'::jsonb;
    total_active numeric := 0;
    total_import numeric := 0;
    v_item jsonb;
    v_new_wbs_codes text[] := '{}';
BEGIN
    -- Ensure authenticated user
    IF auth.uid() IS NULL THEN
        RAISE EXCEPTION 'Unauthorized: auth.uid() is NULL';
    END IF;

    v_user_id := auth.uid();

    -- Get project details and validate access
    SELECT p.active_budget_version_id, p.wbs_library_id, p.client_id
    INTO v_active_version_id, v_wbs_library_id, v_client_id
    FROM public.project p
    WHERE p.project_id = p_project_id;

    IF NOT FOUND THEN
        RAISE EXCEPTION 'Project not found or access denied';
    END IF;

    -- Verify user can access this project
    IF NOT public.can_access_project(p_project_id) THEN
        RAISE EXCEPTION 'Access denied to project';
    END IF;

    -- Calculate total cost for active version
    IF v_active_version_id IS NOT NULL THEN
        SELECT COALESCE(SUM(
            COALESCE(bvi.quantity, 0) *
            COALESCE(bvi.unit_rate, bvi.material_rate, 0) *
            COALESCE(bvi.factor, 1)
        ), 0)
        INTO total_active
        FROM public.budget_version_item bvi
        WHERE bvi.budget_version_id = v_active_version_id;
    END IF;

    -- Calculate total cost for import items and collect new WBS codes
    FOR v_item IN SELECT * FROM jsonb_array_elements(p_items)
    LOOP
        -- Calculate cost for this item
        total_import := total_import + (
            COALESCE((v_item->>'quantity')::numeric, 0) *
            COALESCE((v_item->>'unit_rate')::numeric, (v_item->>'material_rate')::numeric, 0) *
            COALESCE((v_item->>'factor')::numeric, 1)
        );

        -- Check if WBS code exists
        IF NOT EXISTS (
            SELECT 1 FROM public.wbs_library_item wli
            WHERE wli.wbs_library_id = v_wbs_library_id
            AND wli.code = (v_item->>'code')
        ) THEN
            v_new_wbs_codes := array_append(v_new_wbs_codes, v_item->>'code');
        END IF;
    END LOOP;

    -- Build added items (items in import but not in active version)
    result := jsonb_set(result, '{added}', (
        SELECT COALESCE(jsonb_agg(
            jsonb_build_object(
                'code', import_item->>'code',
                'description', import_item->>'description',
                'quantity', (import_item->>'quantity')::numeric,
                'unit', import_item->>'unit',
                'material_rate', (import_item->>'material_rate')::numeric,
                'labor_rate', (import_item->>'labor_rate')::numeric,
                'productivity_per_hour', (import_item->>'productivity_per_hour')::numeric,
                'unit_rate_manual_override', (import_item->>'unit_rate_manual_override')::boolean,
                'unit_rate', (import_item->>'unit_rate')::numeric,
                'factor', (import_item->>'factor')::numeric,
                'remarks', import_item->>'remarks'
            )
        ), '[]'::jsonb)
        FROM jsonb_array_elements(p_items) import_item
        WHERE v_active_version_id IS NULL
        OR NOT EXISTS (
            SELECT 1
            FROM public.budget_version_item bvi
            JOIN public.wbs_library_item wli ON bvi.wbs_library_item_id = wli.wbs_library_item_id
            WHERE bvi.budget_version_id = v_active_version_id
            AND wli.code = (import_item->>'code')
        )
    ));

    -- Build removed items (items in active version but not in import)
    result := jsonb_set(result, '{removed}', (
        SELECT COALESCE(jsonb_agg(
            jsonb_build_object(
                'code', wli.code,
                'description', wli.description,
                'quantity', bvi.quantity,
                'unit', bvi.unit,
                'material_rate', bvi.material_rate,
                'labor_rate', bvi.labor_rate,
                'productivity_per_hour', bvi.productivity_per_hour,
                'unit_rate_manual_override', bvi.unit_rate_manual_override,
                'unit_rate', bvi.unit_rate,
                'factor', bvi.factor,
                'remarks', bvi.remarks
            )
        ), '[]'::jsonb)
        FROM public.budget_version_item bvi
        JOIN public.wbs_library_item wli ON bvi.wbs_library_item_id = wli.wbs_library_item_id
        WHERE bvi.budget_version_id = v_active_version_id
        AND NOT EXISTS (
            SELECT 1
            FROM jsonb_array_elements(p_items) import_item
            WHERE (import_item->>'code') = wli.code
        )
    ));

    -- Build changed items (items that exist in both but have different values)
    result := jsonb_set(result, '{changed}', (
        SELECT COALESCE(jsonb_agg(
            jsonb_build_object(
                'code', wli.code,
                'description', wli.description,
                'quantity', bvi.quantity,
                'unit', bvi.unit,
                'material_rate', bvi.material_rate,
                'labor_rate', bvi.labor_rate,
                'productivity_per_hour', bvi.productivity_per_hour,
                'unit_rate_manual_override', bvi.unit_rate_manual_override,
                'unit_rate', bvi.unit_rate,
                'factor', bvi.factor,
                'remarks', bvi.remarks,
                'diff', jsonb_build_object(
                    'quantity', jsonb_build_object(
                        'old', bvi.quantity,
                        'new', (import_item->>'quantity')::numeric
                    ),
                    'unit', jsonb_build_object(
                        'old', bvi.unit,
                        'new', import_item->>'unit'
                    ),
                    'material_rate', jsonb_build_object(
                        'old', bvi.material_rate,
                        'new', (import_item->>'material_rate')::numeric
                    ),
                    'labor_rate', jsonb_build_object(
                        'old', bvi.labor_rate,
                        'new', (import_item->>'labor_rate')::numeric
                    ),
                    'productivity_per_hour', jsonb_build_object(
                        'old', bvi.productivity_per_hour,
                        'new', (import_item->>'productivity_per_hour')::numeric
                    ),
                    'unit_rate', jsonb_build_object(
                        'old', bvi.unit_rate,
                        'new', (import_item->>'unit_rate')::numeric
                    ),
                    'factor', jsonb_build_object(
                        'old', bvi.factor,
                        'new', (import_item->>'factor')::numeric
                    ),
                    'remarks', jsonb_build_object(
                        'old', bvi.remarks,
                        'new', import_item->>'remarks'
                    )
                )
            )
        ), '[]'::jsonb)
        FROM public.budget_version_item bvi
        JOIN public.wbs_library_item wli ON bvi.wbs_library_item_id = wli.wbs_library_item_id
        JOIN jsonb_array_elements(p_items) import_item ON (import_item->>'code') = wli.code
        WHERE bvi.budget_version_id = v_active_version_id
        AND (
            COALESCE(bvi.quantity, 0) != COALESCE((import_item->>'quantity')::numeric, 0)
            OR COALESCE(bvi.unit, '') != COALESCE(import_item->>'unit', '')
            OR COALESCE(bvi.material_rate, 0) != COALESCE((import_item->>'material_rate')::numeric, 0)
            OR COALESCE(bvi.labor_rate, 0) != COALESCE((import_item->>'labor_rate')::numeric, 0)
            OR COALESCE(bvi.productivity_per_hour, 0) != COALESCE((import_item->>'productivity_per_hour')::numeric, 0)
            OR COALESCE(bvi.unit_rate, 0) != COALESCE((import_item->>'unit_rate')::numeric, 0)
            OR COALESCE(bvi.factor, 1) != COALESCE((import_item->>'factor')::numeric, 1)
            OR COALESCE(bvi.remarks, '') != COALESCE(import_item->>'remarks', '')
        )
    ));

    -- Add new WBS codes array
    result := jsonb_set(result, '{new_wbs_codes}', to_jsonb(v_new_wbs_codes));

    -- Summary
    result := jsonb_set(result, '{summary}', jsonb_build_object(
        'version_a', COALESCE(v_active_version_id::text, 'none'),
        'version_b', 'import_preview',
        'total_cost_a', total_active,
        'total_cost_b', total_import,
        'total_cost_delta', (total_import - total_active)
    ));

    RETURN result;
END;
$function$;

CREATE OR REPLACE FUNCTION public.find_existing_import (p_project_id uuid, p_source_hash text) RETURNS TABLE (
	"exists" boolean,
	budget_import_id uuid,
	version_id uuid,
	created_at timestamp with time zone,
	filename text
) LANGUAGE sql
SET
	search_path TO '' AS $function$
    -- Ensure authenticated user has access to project
    SELECT
        CASE WHEN bi.budget_import_id IS NOT NULL THEN true ELSE false END as exists,
        bi.budget_import_id,
        bi.new_version_id as version_id,
        bi.created_at,
        bi.source_filename as filename
    FROM public.budget_import bi
    WHERE bi.project_id = p_project_id
    AND bi.source_hash = p_source_hash
    AND public.can_access_project(p_project_id)
    ORDER BY bi.created_at DESC
    LIMIT 1;
$function$;
