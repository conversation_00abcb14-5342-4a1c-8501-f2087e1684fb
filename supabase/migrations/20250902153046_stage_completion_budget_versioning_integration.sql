drop function if exists "public"."create_budget_snapshot" (p_project_stage_id uuid, p_freeze_reason text);

set
	check_function_bodies = off;

CREATE OR REPLACE FUNCTION public.create_budget_snapshot (
	p_project_stage_id uuid,
	p_freeze_reason text DEFAULT NULL::text,
	p_budget_version_id uuid DEFAULT NULL::uuid
) RETURNS uuid LANGUAGE plpgsql SECURITY DEFINER
SET
	search_path TO '' AS $function$
DECLARE
	v_project_id UUID;
	v_snapshot_id UUID;
	v_item RECORD;
	v_user_id UUID;
	v_budget_version_id UUID;
BEGIN
	-- Get the current user ID, fallback to a system user if not authenticated
	v_user_id := auth.uid();
	IF v_user_id IS NULL THEN
		-- Use a system user ID for operations not performed by authenticated users
		v_user_id := '00000000-0000-0000-0000-000000000000'::UUID;
	END IF;

	-- Get the project_id from the stage
	SELECT project_id INTO v_project_id
	FROM public.project_stage
	WHERE project_stage_id = p_project_stage_id;

	IF v_project_id IS NULL THEN
		RAISE EXCEPTION 'Project stage not found';
	END IF;

	-- Use provided budget version ID or fall back to active version
	v_budget_version_id := p_budget_version_id;
	IF v_budget_version_id IS NULL THEN
		SELECT active_budget_version_id INTO v_budget_version_id
		FROM public.project
		WHERE project_id = v_project_id;
	END IF;

	INSERT INTO public.budget_snapshot (
		project_stage_id,
		freeze_date,
		freeze_reason,
		created_by_user_id,
		budget_version_id
	)
	VALUES (
		p_project_stage_id,
		now(),
		p_freeze_reason,
		v_user_id,
		v_budget_version_id
	)
	RETURNING budget_snapshot_id INTO v_snapshot_id;

	-- Copy budget items from the specified budget version (or active version if none specified)
	IF v_budget_version_id IS NOT NULL THEN
		-- Copy from budget version items
		FOR v_item IN (
			SELECT *
			FROM public.budget_version_item
			WHERE budget_version_id = v_budget_version_id
		) LOOP
			INSERT INTO public.budget_snapshot_line_item (
				budget_snapshot_id,
				wbs_library_item_id,
				quantity,
				unit,
				material_rate,
				labor_rate,
				productivity_per_hour,
				unit_rate_manual_override,
				unit_rate,
				factor,
				remarks,
				cost_certainty,
				design_certainty
			)
			VALUES (
				v_snapshot_id,
				v_item.wbs_library_item_id,
				v_item.quantity,
				v_item.unit,
				v_item.material_rate,
				v_item.labor_rate,
				v_item.productivity_per_hour,
				v_item.unit_rate_manual_override,
				v_item.unit_rate,
				v_item.factor,
				v_item.remarks,
				v_item.cost_certainty,
				v_item.design_certainty
			);
		END LOOP;
	ELSE
		-- Fallback to current budget line items if no version specified
		FOR v_item IN (
			SELECT *
			FROM public.budget_line_item_current
			WHERE project_id = v_project_id
		) LOOP
			INSERT INTO public.budget_snapshot_line_item (
				budget_snapshot_id,
				wbs_library_item_id,
				quantity,
				unit,
				material_rate,
				labor_rate,
				productivity_per_hour,
				unit_rate_manual_override,
				unit_rate,
				factor,
				remarks,
				cost_certainty,
				design_certainty
			)
			VALUES (
				v_snapshot_id,
				v_item.wbs_library_item_id,
				v_item.quantity,
				v_item.unit,
				v_item.material_rate,
				v_item.labor_rate,
				v_item.productivity_per_hour,
				v_item.unit_rate_manual_override,
				v_item.unit_rate,
				v_item.factor,
				v_item.remarks,
				v_item.cost_certainty,
				v_item.design_certainty
			);
		END LOOP;
	END IF;
	
	RETURN v_snapshot_id;
END;
$function$;

CREATE OR REPLACE FUNCTION public.complete_project_stage (
	p_project_stage_id uuid,
	p_completion_notes text DEFAULT NULL::text
) RETURNS uuid LANGUAGE plpgsql SECURITY DEFINER
SET
	search_path TO '' AS $function$
DECLARE
	v_snapshot_id UUID;
	v_project_id UUID;
	v_is_ready BOOLEAN;
	v_stage_version_id UUID;
	v_active_version_id UUID;
	v_user_id UUID;
	v_stage_name TEXT;
BEGIN
	-- Get current user ID
	v_user_id := auth.uid();
	IF v_user_id IS NULL THEN
		RAISE EXCEPTION 'Unauthorized: auth.uid() is NULL';
	END IF;

	-- Get project_id and stage name for permission check and labeling
	SELECT ps.project_id, ps.name INTO v_project_id, v_stage_name
	FROM public.project_stage ps
	WHERE ps.project_stage_id = p_project_stage_id;

	IF v_project_id IS NULL THEN
		RAISE EXCEPTION 'Project stage not found';
	END IF;

	-- Check if user can modify this project
	IF NOT public.can_modify_project(v_project_id) THEN
		RAISE EXCEPTION 'Insufficient permissions to complete this project stage';
	END IF;

	-- Check if stage is ready for completion
	SELECT public.is_stage_ready_for_completion(p_project_stage_id) INTO v_is_ready;

	IF NOT v_is_ready THEN
		RAISE EXCEPTION 'Project stage has incomplete checklist items and cannot be completed';
	END IF;

	-- Get current active budget version for lineage
	SELECT active_budget_version_id INTO v_active_version_id
	FROM public.project
	WHERE project_id = v_project_id;

	-- Create a budget version for this stage completion
	INSERT INTO public.budget_version (
		project_id,
		label,
		kind,
		stage_id,
		prev_version_id,
		created_by_user_id
	) VALUES (
		v_project_id,
		'Stage Completion: ' || v_stage_name,
		'stage'::public.budget_version_kind,
		p_project_stage_id,
		v_active_version_id,
		v_user_id
	) RETURNING budget_version_id INTO v_stage_version_id;

	-- Copy budget items from active version to the new stage version
	INSERT INTO public.budget_version_item (
		budget_version_id,
		wbs_library_item_id,
		quantity,
		unit,
		material_rate,
		labor_rate,
		productivity_per_hour,
		unit_rate_manual_override,
		unit_rate,
		factor,
		remarks,
		cost_certainty,
		design_certainty
	)
	SELECT
		v_stage_version_id,
		bvi.wbs_library_item_id,
		bvi.quantity,
		bvi.unit,
		bvi.material_rate,
		bvi.labor_rate,
		bvi.productivity_per_hour,
		bvi.unit_rate_manual_override,
		bvi.unit_rate,
		bvi.factor,
		bvi.remarks,
		bvi.cost_certainty,
		bvi.design_certainty
	FROM public.budget_version_item bvi
	WHERE bvi.budget_version_id = v_active_version_id;

	-- Update the stage
	UPDATE public.project_stage
	SET
		date_completed = now(),
		completion_notes = p_completion_notes,
		updated_at = now()
	WHERE project_stage_id = p_project_stage_id;

	-- Create a budget snapshot linked to the stage version
	SELECT public.create_budget_snapshot(p_project_stage_id, p_completion_notes, v_stage_version_id) INTO v_snapshot_id;

	RETURN v_snapshot_id;
END;
$function$;
